package com.nocetfy.tools.controller;

import com.nocetfy.tools.dao.rpc.LogInsightRespV2;
import com.nocetfy.tools.dao.rpc.TraceLogParamV2;
import com.nocetfy.tools.service.LogInsightService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 日志洞察控制器
 */
@RestController
@RequestMapping("/log-insight")
@RequiredArgsConstructor
public class LogInsightController {
    
    private final LogInsightService logInsightService;
    
    /**
     * 查询日志追踪信息
     *
     * @param param 查询参数
     * @return 查询结果
     */
    @PostMapping("/query")
    public LogInsightRespV2 queryTraceLogs(@RequestBody TraceLogParamV2 param) {
        return logInsightService.queryTraceLogs(param);
    }

    /**
     * 根据TraceId查询日志
     *
     * @param traceId 链路追踪ID
     * @return 查询结果
     */
    @GetMapping("/trace/{traceId}")
    public LogInsightRespV2 queryLogsByTraceId(@PathVariable String traceId) {
        return logInsightService.queryLogsByTraceId(traceId);
    }

    /**
     * 根据服务名和时间范围查询日志
     *
     * @param odinService 服务名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    @GetMapping("/service/{odinService}")
    public LogInsightRespV2 queryLogsByService(
            @PathVariable String odinService,
            @RequestParam Long startTime,
            @RequestParam Long endTime) {
        return logInsightService.queryLogsByService(odinService, startTime, endTime);
    }

    /**
     * 使用自定义认证信息查询日志追踪信息
     *
     * @param accessAuth 访问认证
     * @param accessSecret 访问密钥
     * @param param 查询参数
     * @return 查询结果
     */
    @PostMapping("/query-with-auth")
    public LogInsightRespV2 queryTraceLogsWithAuth(
            @RequestHeader("access_auth") String accessAuth,
            @RequestHeader("access_secret") String accessSecret,
            @RequestBody TraceLogParamV2 param) {
        return logInsightService.queryTraceLogsWithAuth(accessAuth, accessSecret, param);
    }
}
