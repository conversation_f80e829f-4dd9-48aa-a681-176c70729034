package com.nocetfy.tools.service;

import com.nocetfy.tools.dao.rpc.LogInsight;
import com.nocetfy.tools.dao.rpc.TraceLogParamV2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 日志洞察服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LogInsightService {
    
    private final LogInsight logInsightClient;
    
    /**
     * 查询日志追踪信息
     * 
     * @param param 查询参数
     * @return 查询结果
     */
    public String queryTraceLogs(TraceLogParamV2 param) {
        try {
            log.info("开始查询日志追踪信息，参数: {}", param);
            String result = logInsightClient.queryTraceLogsWithObject(param);
            log.info("查询日志追踪信息完成");
            return result;
        } catch (Exception e) {
            log.error("查询日志追踪信息失败", e);
            throw new RuntimeException("查询日志追踪信息失败", e);
        }
    }
    
    /**
     * 根据TraceId查询日志
     * 
     * @param traceId 链路追踪ID
     * @return 查询结果
     */
    public String queryLogsByTraceId(String traceId) {
        TraceLogParamV2 param = new TraceLogParamV2();
        param.setTraceId(traceId);
        param.setPageSize(100);
        param.setIsAsc(false);
        
        return queryTraceLogs(param);
    }
    
    /**
     * 根据服务名和时间范围查询日志
     * 
     * @param odinService 服务名
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 查询结果
     */
    public String queryLogsByService(String odinService, Long startTime, Long endTime) {
        TraceLogParamV2 param = new TraceLogParamV2();
        param.setOdinService(odinService);
        param.setStartTime(startTime);
        param.setEndTime(endTime);
        param.setPageSize(100);
        param.setIsAsc(false);
        
        return queryTraceLogs(param);
    }
}
