package com.nocetfy.tools.dao.rpc;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.HttpExchange;

/**
 * 日志洞察HTTP RPC接口
 */
@HttpExchange
public interface LogInsight {

    /**
     * 查询日志追踪信息
     *
     * @param accessAuth 访问认证
     * @param accessSecret 访问密钥
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param odinService Odin服务名
     * @param traceId 链路追踪ID
     * @param uri URI路径
     * @param pageSize 页面大小
     * @param dltag DL标签
     * @param isAsc 是否升序
     * @param messagekey 消息键
     * @param idc IDC机房标识
     * @return 查询结果
     */
    @GetMapping("/api/v2/trace-logs")
    LogInsightRespV2 queryTraceLogs(
            @RequestHeader("access_auth") String accessAuth,
            @RequestHeader("access_secret") String accessSecret,
            @RequestParam(value = "startTime", required = false) Long startTime,
            @RequestParam(value = "endTime", required = false) Long endTime,
            @RequestParam(value = "odinService", required = false) String odinService,
            @RequestParam(value = "traceId", required = false) String traceId,
            @RequestParam(value = "uri", required = false) String uri,
            @RequestParam(value = "pageSize", required = false) Integer pageSize,
            @RequestParam(value = "dltag", required = false) String dltag,
            @RequestParam(value = "isAsc", required = false) Boolean isAsc,
            @RequestParam(value = "messageKey", required = false) String messagekey,
            @RequestParam(value = "idc", required = false) Integer idc
    );

    /**
     * 查询日志追踪信息 - 使用对象参数
     *
     * @param accessAuth 访问认证
     * @param accessSecret 访问密钥
     * @param param 查询参数对象
     * @return 查询结果
     */
    @GetMapping("/api/v2/trace-logs-obj")
    LogInsightRespV2 queryTraceLogsWithObject(
            @RequestHeader("access_auth") String accessAuth,
            @RequestHeader("access_secret") String accessSecret,
            TraceLogParamV2 param
    );
}
