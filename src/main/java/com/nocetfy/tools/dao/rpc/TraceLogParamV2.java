package com.nocetfy.tools.dao.rpc;

import lombok.Data;

/**
 * 日志查询参数V2
 */
@Data
public class TraceLogParamV2 {
    
    /**
     * 开始时间
     */
    private Long startTime;
    
    /**
     * 结束时间
     */
    private Long endTime;
    
    /**
     * Odin服务名
     */
    private String odinService;
    
    /**
     * 链路追踪ID
     */
    private String traceId;
    
    /**
     * URI路径
     */
    private String uri;
    
    /**
     * 页面大小
     */
    private Integer pageSize;
    
    /**
     * DL标签
     */
    private String dltag;
    
    /**
     * 是否升序
     */
    private Boolean isAsc;
    
    /**
     * 消息键
     */
    private String messagekey;
    
    /**
     * IDC机房标识
     */
    private Integer idc;
}
