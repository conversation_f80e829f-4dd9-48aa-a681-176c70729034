package com.nocetfy.tools.dao.rpc;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 日志洞察响应V2
 */
public record LogInsightRespV2(
        @JsonProperty("data") Data data,
        @JsonProperty("metaInfo") MetaInfo metaInfo
) {
    
    /**
     * 响应数据
     */
    public record Data(
            @JsonProperty("list") List<LogInsightItem> list
    ) {}
    
    /**
     * 元信息
     */
    public record MetaInfo(
            @JsonProperty("code") int code,
            @JsonProperty("msg") String msg
    ) {}
}
