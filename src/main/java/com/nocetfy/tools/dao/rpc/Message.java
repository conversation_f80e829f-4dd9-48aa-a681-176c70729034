package com.nocetfy.tools.dao.rpc;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 消息详情
 */
public record Message(
        @JsonProperty("log_level") String logLevel,
        @JsonProperty("log_time") String logTime,
        @JsonProperty("line") String line,
        @JsonProperty("dltag") String dltag,
        @JsonProperty("traceid") String traceId,
        @JsonProperty("hintCode") String hintCode,
        @JsonProperty("spanid") String spanId,
        @JsonProperty("logid") String logId,
        @JsonProperty("sampling") String sampling,
        @JsonProperty("uri") String uri,
        @JsonProperty("module") String module,
        @JsonProperty("controller") String controller,
        @JsonProperty("hintContent") String hintContent,
        @JsonProperty("url") String url,
        @JsonProperty("from") String from,
        @JsonProperty("args") String args,
        @JsonProperty("response") String response,
        @JsonProperty("trace_origin") String traceOrigin,
        @JsonProperty("caller") String caller,
        @JsonProperty("callee") String callee,
        @JsonProperty("rpc_name") String rpcName,
        @JsonProperty("caller_func") String callerFunc,
        @JsonProperty("callee_func") String calleeFunc,
        @JsonProperty("request") String request
) {}
