package com.nocetfy.tools.dao.rpc;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 日志洞察项
 */
public record LogInsightItem(
        @JsonProperty("logTime") String logTime,
        @JsonProperty("logTimeStamp") Long logTimeStamp,
        @JsonProperty("hostName") String hostName,
        @JsonProperty("dltag") String dltag,
        @JsonProperty("odinSu") String odinSu,
        @JsonProperty("appName") String appName,
        @JsonProperty("odinLeaf") String odinLeaf,
        @JsonProperty("traceId") String traceId,
        @JsonProperty("spanId") String spanId,
        @JsonProperty("cspanId") String cspanId,
        @JsonProperty("logName") String logName,
        @JsonProperty("extractLevel") String extractLevel,
        @JsonProperty("message") String message,
        @JsonProperty("msg") Message msg,
        @JsonProperty("errNo") String errNo,
        @JsonProperty("errMsg") String errMsg,
        @JsonProperty("uri") String uri
) {}
