package com.nocetfy.tools.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * LogInsight认证配置
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "log-insight.auth")
public class LogInsightAuthConfig {
    
    /**
     * 访问认证
     */
    private String accessAuth;
    
    /**
     * 访问密钥
     */
    private String accessSecret;
    
    /**
     * 服务基础URL
     */
    private String baseUrl = "http://your-log-insight-service-url";
}
