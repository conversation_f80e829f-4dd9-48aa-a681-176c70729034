package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * HTTP客户端配置
 */
@Configuration
public class HttpClientConfig {
    
    /**
     * 创建LogInsight HTTP RPC客户端
     *
     * @return LogInsight客户端实例
     */
    @Bean
    public LogInsight logInsightClient() {
        WebClient webClient = WebClient.builder()
                .baseUrl("http://your-log-insight-service-url") // 替换为实际的服务URL
                .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
                .builderFor(WebClient.class)
                .build();

        return factory.createClient(LogInsight.class);
    }
}
