package com.nocetfy.tools.config;

import com.nocetfy.tools.dao.rpc.LogInsight;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;

/**
 * HTTP客户端配置
 */
@Configuration
@RequiredArgsConstructor
public class HttpClientConfig {

    private final LogInsightAuthConfig authConfig;
    
    /**
     * 创建LogInsight HTTP RPC客户端
     *
     * @return LogInsight客户端实例
     */
    @Bean
    public LogInsight logInsightClient() {
        WebClient webClient = WebClient.builder()
                .baseUrl(authConfig.getBaseUrl())
                .defaultHeader("access_auth", authConfig.getAccessAuth())
                .defaultHeader("access_secret", authConfig.getAccessSecret())
                .build();

        HttpServiceProxyFactory factory = HttpServiceProxyFactory
                .builderFor(WebClient.class)
                .build();

        return factory.createClient(LogInsight.class);
    }
}
